<script setup lang="ts">
// Color test component to demonstrate the custom primary color system
</script>

<template>
  <div class="p-8 space-y-6 bg-white rounded-lg shadow-lg">
    <h2 class="text-3xl font-bold text-primary-700 mb-6">
      Primary Color System Test
    </h2>
    
    <!-- Color Palette Display -->
    <div class="space-y-4">
      <h3 class="text-xl font-semibold text-gray-700">Color Shades</h3>
      <div class="grid grid-cols-6 gap-2">
        <div class="bg-primary-50 h-16 rounded flex items-center justify-center text-primary-900 text-sm font-medium">
          50
        </div>
        <div class="bg-primary-100 h-16 rounded flex items-center justify-center text-primary-900 text-sm font-medium">
          100
        </div>
        <div class="bg-primary-300 h-16 rounded flex items-center justify-center text-white text-sm font-medium">
          300
        </div>
        <div class="bg-primary-500 h-16 rounded flex items-center justify-center text-white text-sm font-medium">
          500
        </div>
        <div class="bg-primary-700 h-16 rounded flex items-center justify-center text-white text-sm font-medium">
          700
        </div>
        <div class="bg-primary-900 h-16 rounded flex items-center justify-center text-white text-sm font-medium">
          900
        </div>
      </div>
    </div>

    <!-- Button Examples -->
    <div class="space-y-4">
      <h3 class="text-xl font-semibold text-gray-700">Button Examples</h3>
      <div class="flex flex-wrap gap-4">
        <button class="btn-primary">
          Primary Button
        </button>
        <button class="btn-primary-outline">
          Outline Button
        </button>
        <button class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded transition-colors">
          Custom Hover
        </button>
        <button class="bg-accent-500 hover:bg-accent-600 text-white font-bold py-2 px-4 rounded transition-colors">
          Accent Button
        </button>
      </div>
    </div>

    <!-- Text Examples -->
    <div class="space-y-4">
      <h3 class="text-xl font-semibold text-gray-700">Text Examples</h3>
      <div class="space-y-2">
        <p class="text-primary-500 font-medium">Primary text color (500)</p>
        <p class="text-primary-600 font-medium">Primary text color (600)</p>
        <p class="text-primary-700 font-medium">Primary text color (700)</p>
        <p class="text-accent-500 font-medium">Accent text color</p>
      </div>
    </div>

    <!-- Border Examples -->
    <div class="space-y-4">
      <h3 class="text-xl font-semibold text-gray-700">Border Examples</h3>
      <div class="grid grid-cols-2 gap-4">
        <div class="border-2 border-primary-500 p-4 rounded">
          Primary border
        </div>
        <div class="border-2 border-accent-500 p-4 rounded">
          Accent border
        </div>
      </div>
    </div>

    <!-- Background Examples -->
    <div class="space-y-4">
      <h3 class="text-xl font-semibold text-gray-700">Background Examples</h3>
      <div class="grid grid-cols-3 gap-4">
        <div class="bg-primary-100 p-4 rounded text-primary-900">
          Light primary background
        </div>
        <div class="bg-primary-500 p-4 rounded text-white">
          Primary background
        </div>
        <div class="bg-primary-900 p-4 rounded text-white">
          Dark primary background
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Additional component-specific styles if needed */
</style>
