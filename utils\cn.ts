import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Utility function that combines clsx and tailwind-merge
 *
 * This function:
 * 1. Uses clsx to handle conditional classes and various input formats
 * 2. Uses tailwind-merge to resolve conflicting Tailwind classes
 *
 * @param inputs - Class values (strings, objects, arrays, etc.)
 * @returns Merged and deduplicated class string
 *
 * @example
 * ```ts
 * // Basic usage
 * cn("px-2 py-1", "bg-red-500")
 * // → "px-2 py-1 bg-red-500"
 *
 * // Conditional classes
 * cn("px-2 py-1", {
 *   "bg-red-500": isError,
 *   "bg-green-500": isSuccess
 * })
 *
 * // Conflicting classes (tailwind-merge resolves conflicts)
 * cn("px-2 px-4", "py-1 py-2")
 * // → "px-4 py-2" (later classes win)
 *
 * // Complex example
 * cn(
 *   "px-2 py-1 bg-blue-500",
 *   isLarge && "px-4 py-2",
 *   {
 *     "bg-red-500": isError,
 *     "bg-green-500": isSuccess,
 *   },
 *   className // from props
 * )
 * ```
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
