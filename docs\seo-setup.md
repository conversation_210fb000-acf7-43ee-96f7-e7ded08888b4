# SEO and Meta Tags Setup

This document outlines the SEO improvements and meta tag configurations implemented in the NuxtEvent Nuxt application.

## Overview

The application now includes comprehensive SEO optimization with proper meta tags, Open Graph support, and PWA capabilities.

## Implemented Features

### 1. Global Head Configuration (nuxt.config.ts)

- **Basic Meta Tags**:

  - Charset: UTF-8
  - Viewport: Responsive design support
  - Description: SEO-friendly description
  - Keywords: Relevant search terms
  - Author: Application author
  - Robots: Search engine indexing instructions

- **Open Graph Meta Tags**:

  - og:type: website
  - og:title: Application title
  - og:description: Application description
  - og:image: Social media sharing image
  - og:url: Canonical URL
  - og:site_name: Site name

- **Twitter Card Meta Tags**:

  - twitter:card: Large image card
  - twitter:title: Twitter-specific title
  - twitter:description: Twitter-specific description
  - twitter:image: Twitter sharing image

- **Additional SEO Meta Tags**:
  - theme-color: Browser theme color
  - msapplication-TileColor: Windows tile color
  - application-name: Application name

### 2. Favicon and Icon Configuration

- Standard favicon.ico
- Apple touch icon (180x180)
- PNG favicons (16x16, 32x32)
- Web app manifest

### 3. Progressive Web App (PWA) Support

- Web manifest file (`/public/site.webmanifest`)
- PWA-ready configuration
- Mobile-optimized icons
- Standalone display mode

### 4. Dynamic Head Management (app.vue)

- Page-specific title using `useHead()`
- Dynamic meta descriptions
- Override global settings per page
- Social media optimization

## File Structure

```
public/
├── favicon.ico             # Standard favicon
├── site.webmanifest        # PWA manifest
└── robots.txt              # Search engine instructions

nuxt.config.ts              # Global head configuration
app.vue                     # Dynamic head management
```

## Required Assets

To complete the SEO setup, add these image files to the `/public` directory:

- `og-image.jpg` (1200x630px) - Open Graph sharing image
- `apple-touch-icon.png` (180x180px) - Apple touch icon
- `favicon-16x16.png` (16x16px) - Small favicon
- `favicon-32x32.png` (32x32px) - Standard favicon
- `android-chrome-192x192.png` (192x192px) - Android icon
- `android-chrome-512x512.png` (512x512px) - Large Android icon

## Best Practices Implemented

1. **Responsive Design**: Proper viewport meta tag
2. **Social Media Optimization**: Complete Open Graph and Twitter Card setup
3. **Search Engine Optimization**: Relevant meta descriptions and keywords
4. **Progressive Web App**: Manifest file and mobile optimization
5. **Performance**: Optimized meta tag structure
6. **Accessibility**: Proper semantic markup in head section

## Usage Examples

### Setting Page-Specific Meta Tags

```vue
<script setup lang="ts">
useHead({
  title: "Custom Page Title - NuxtEvent",
  meta: [
    {
      name: "description",
      content: "Custom page description for better SEO",
    },
    {
      property: "og:title",
      content: "Custom Page Title - NuxtEvent",
    },
  ],
});
</script>
```

### Dynamic Meta Tags

```vue
<script setup lang="ts">
const event = await $fetch("/api/events/123");

useHead({
  title: `${event.title} - NuxtEvent`,
  meta: [
    {
      name: "description",
      content: event.description,
    },
    {
      property: "og:image",
      content: event.image,
    },
  ],
});
</script>
```

## Testing SEO Implementation

1. **Google Search Console**: Submit sitemap and monitor indexing
2. **Facebook Debugger**: Test Open Graph tags
3. **Twitter Card Validator**: Verify Twitter sharing
4. **Lighthouse**: Check SEO score and performance
5. **Mobile-Friendly Test**: Ensure mobile optimization

## Environment-Specific Configuration

Update the following values based on your environment:

- `og:url`: Replace with actual domain
- `og:image`: Use absolute URLs in production
- `twitter:site`: Add Twitter handle if available
- Canonical URLs: Implement for duplicate content prevention

## Next Steps

1. Add structured data (JSON-LD) for rich snippets
2. Implement sitemap generation
3. Add analytics tracking
4. Configure canonical URLs
5. Set up redirects for SEO
