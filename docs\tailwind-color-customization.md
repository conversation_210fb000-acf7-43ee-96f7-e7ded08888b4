# Tailwind CSS Color Customization Guide

## Overview
This guide demonstrates how to customize primary colors in your Tailwind CSS configuration with various approaches and best practices.

## Current Configuration

Your `tailwind.config.js` now includes custom primary and accent colors:

```javascript
theme: {
  extend: {
    colors: {
      // Custom primary color with multiple shades
      primary: {
        50: '#eff6ff',
        100: '#dbeafe',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6', // Main primary color
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a',
        950: '#172554',
      },
    },
  },
},
```

## Color Definition Methods

### 1. Multiple Shades (Recommended)
```javascript
primary: {
  50: '#eff6ff',   // Lightest
  100: '#dbeafe',
  200: '#bfdbfe',
  300: '#93c5fd',
  400: '#60a5fa',
  500: '#3b82f6',  // Base color
  600: '#2563eb',
  700: '#1d4ed8',
  800: '#1e40af',
  900: '#1e3a8a',
  950: '#172554',  // Darkest
},
```

### 2. Single Color
```javascript
primary: '#3b82f6',
```

### 3. RGB/HSL Values
```javascript
primary: {
  500: 'rgb(59, 130, 246)',
  600: 'hsl(217, 91%, 60%)',
},
```

### 4. CSS Custom Properties
```javascript
primary: {
  500: 'var(--color-primary)',
  600: 'var(--color-primary-dark)',
},
```

## Usage Examples

### In HTML Classes
```html
<!-- Background colors -->
<div class="bg-primary-500">Main primary background</div>
<div class="bg-primary-100">Light primary background</div>
<div class="bg-primary-900">Dark primary background</div>

<!-- Text colors -->
<p class="text-primary-500">Primary text</p>
<p class="text-primary-700">Darker primary text</p>

<!-- Border colors -->
<div class="border-primary-500 border-2">Primary border</div>

<!-- Hover states -->
<button class="bg-primary-500 hover:bg-primary-600">
  Hover effect
</button>
```

### In CSS with @apply
```css
.btn-primary {
  @apply bg-primary-500 text-white font-bold py-2 px-4 rounded;
}

.btn-primary:hover {
  @apply bg-primary-600;
}
```

## Color Palette Tools

### Online Tools for Generating Color Palettes:
1. **Tailwind Color Generator**: https://uicolors.app/create
2. **Coolors.co**: https://coolors.co/
3. **Adobe Color**: https://color.adobe.com/
4. **Material Design Color Tool**: https://material.io/resources/color/

### Recommended Color Combinations:
```javascript
// Blue theme (current)
primary: '#3b82f6',

// Green theme
primary: '#10b981',

// Purple theme
primary: '#8b5cf6',

// Orange theme
primary: '#f59e0b',

// Red theme
primary: '#ef4444',
```

## Best Practices

1. **Use semantic naming**: `primary`, `secondary`, `accent` instead of color names
2. **Provide multiple shades**: 50-950 range for flexibility
3. **Consider accessibility**: Ensure sufficient contrast ratios
4. **Test in dark mode**: If supporting dark themes
5. **Use CSS custom properties**: For dynamic theming

## Integration with Nuxt UI

Since you're using Nuxt UI, you can also customize colors through the `app.config.ts`:

```typescript
export default defineAppConfig({
  ui: {
    primary: 'blue', // or your custom color name
    gray: 'slate'
  }
})
```

## Testing Your Colors

Create a test component to verify your color system:

```vue
<template>
  <div class="p-8 space-y-4">
    <h2 class="text-2xl font-bold text-primary-700">Color Test</h2>
    
    <!-- Primary color variations -->
    <div class="grid grid-cols-5 gap-2">
      <div v-for="shade in [100, 300, 500, 700, 900]" 
           :key="shade"
           :class="`bg-primary-${shade} h-16 rounded flex items-center justify-center text-white`">
        {{ shade }}
      </div>
    </div>
    
    <!-- Button examples -->
    <div class="space-x-4">
      <button class="btn-primary">Primary Button</button>
      <button class="btn-primary-outline">Outline Button</button>
    </div>
  </div>
</template>
```
