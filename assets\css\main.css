@import "tailwindcss";
@import "@nuxt/ui";

/* Custom base styles */
@layer base {
  html,
  body {
    font-family: sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #ffffff;
  }
}
/* Custom components */
@layer components {
  .btn-primary {
    background-color: rgb(59, 130, 246);
    color: white;
    font-weight: bold;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
  }
  .btn-primary:hover {
    background-color: rgb(29, 78, 216);
  }
}
/* Custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }
}
