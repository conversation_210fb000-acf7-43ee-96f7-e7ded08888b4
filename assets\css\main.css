@import "tailwindcss";
@import "@nuxt/ui";

/* Custom base styles */
@layer base {
  html,
  body {
    font-family: sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #ffffff;
  }
}
/* Custom components */
@layer components {
  .btn-primary {
    @apply bg-primary-500 rounded px-4 py-2 font-bold text-white;
  }
  .btn-primary:hover {
    @apply bg-primary-600;
  }

  /* Additional primary color components */
  .btn-primary-outline {
    @apply border-primary-500 text-primary-500 rounded border-2 px-4 py-2 font-bold;
  }
  .btn-primary-outline:hover {
    @apply bg-primary-500 text-white;
  }

  .text-primary {
    @apply text-primary-500;
  }

  .bg-primary {
    @apply bg-primary-500;
  }
}
/* Custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }
}
