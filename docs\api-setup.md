# API Client Setup with Apisauce

This document explains how the API client is configured in this NUXT 3 project using apisauce.

## Overview

The project uses [apisauce](https://github.com/infinitered/apisauce) as the HTTP client library, which provides a lightweight wrapper around Axios with additional features like:

- Automatic request/response transformations
- Built-in error handling
- Request/response monitoring
- TypeScript support

## Configuration

### Environment Variables

The API base URL is configured via environment variables:

```bash
# .env
API_BASE_URL=https://your-api.com
```

### NUXT Runtime Config

The environment variable is exposed through NUXT's runtime config in `nuxt.config.ts`:

```typescript
export default defineNuxtConfig({
  runtimeConfig: {
    // Private keys (only available on server-side)
    apiBaseUrl: process.env.API_BASE_URL || "http://localhost:3000/api",

    // Public keys (exposed to client-side)
    public: {
      apiBaseUrl: process.env.API_BASE_URL || "http://localhost:3000/api",
    },
  },
});
```

## File Structure

```
utils/
  api.ts              # Core API client configuration
composables/
  useApi.ts           # NUXT composable for API operations
```

## Usage Examples

### Basic Usage with useApi Composable

```vue
<script setup lang="ts">
const { http } = useApi();

// GET request
const products = await http.get("/products");

// POST request
const newProduct = await http.post("/products", {
  title: "New Product",
  price: 29.99,
});

// PUT request
const updatedProduct = await http.put("/products/1", {
  title: "Updated Product",
});

// DELETE request
await http.delete("/products/1");
</script>
```

### Using Resource-Specific Composables

```vue
<script setup lang="ts">
const { getProducts, getProduct } = useProducts();

// Fetch all products
const products = await getProducts();

// Fetch single product
const product = await getProduct(1);
</script>
```

### Advanced Usage with Direct Client Access

```vue
<script setup lang="ts">
const { client } = useApi();

// Add custom headers
client.setHeader("Authorization", "Bearer your-token");

// Add request monitor
client.addMonitor((response) => {
  console.log("API Response:", response);
});

// Make custom request
const response = await client.get("/custom-endpoint", {
  param1: "value1",
});
</script>
```

### Error Handling

The API client includes built-in error handling:

```vue
<script setup lang="ts">
const { http } = useApi();

try {
  const data = await http.get("/products");
  // Handle success
} catch (error) {
  // Error is automatically handled and thrown with a meaningful message
  console.error("API Error:", error.message);
}
</script>
```

### Using with NUXT Data Fetching

```vue
<script setup lang="ts">
const { getProducts } = useProducts();

// Server-side rendering with caching
const {
  data: products,
  pending,
  error,
} = await useLazyAsyncData("products", () => getProducts());

// Refresh data
const refresh = () => refreshCookie("products");
</script>
```

## Features

### Request/Response Interceptors

The API client includes built-in interceptors for:

- **Request logging** (development mode only)
- **Response logging** (development mode only)
- **Error logging** with detailed information
- **Authentication headers** (ready for implementation)

### TypeScript Support

All API functions are fully typed:

```typescript
interface Product {
  id: number;
  title: string;
  price: number;
  description: string;
  category: string;
  image: string;
  rating: {
    rate: number;
    count: number;
  };
}

// Typed API calls
const products = await http.get<Product[]>("/products");
const product = await http.get<Product>("/products/1");
```

### Environment-Based Configuration

The API client automatically adapts to different environments:

- **Development**: `http://localhost:3000/api` (default)
- **Production**: Set via `API_BASE_URL` environment variable
- **Testing**: Can be overridden for testing environments

## Customization

### Adding Authentication

To add authentication headers:

```typescript
// In your auth composable or plugin
const { client } = useApi();

// Set bearer token
client.setHeader("Authorization", `Bearer ${token}`);

// Or add to all requests
client.addRequestTransform((request) => {
  const token = getAuthToken(); // Your token retrieval logic
  if (token) {
    request.headers["Authorization"] = `Bearer ${token}`;
  }
});
```

### Custom Error Handling

```typescript
// In utils/api.ts, modify the handleApiError function
export function handleApiError(response: ApiResponse<any>) {
  if (!response.ok) {
    // Custom error handling logic
    if (response.status === 401) {
      // Handle unauthorized
      navigateTo("/login");
    }

    const errorMessage =
      response.data?.message ||
      response.problem ||
      "An unexpected error occurred";

    throw new Error(errorMessage);
  }

  return response.data;
}
```

## Best Practices

1. **Use composables** for resource-specific operations
2. **Handle errors gracefully** with try-catch blocks
3. **Type your API responses** for better development experience
4. **Use NUXT data fetching** for SSR and caching benefits
5. **Keep API logic separate** from component logic
6. **Use environment variables** for different API endpoints
