{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@nuxt/ui": "^3.1.3", "apisauce": "^3.1.1", "clsx": "^2.1.1", "nuxt": "^3.17.4", "tailwind-merge": "^3.3.0", "vue": "^3.5.15", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/lucide": "^1.2.47", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12"}}