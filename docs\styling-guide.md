# Styling Guide: clsx + tailwind-merge + Prettier

This project uses a powerful combination of utilities for managing Tailwind CSS classes:

- **`clsx`**: For conditional and dynamic class handling
- **`tailwind-merge`**: For resolving conflicting Tailwind classes
- **`prettier-plugin-tailwindcss`**: For automatic class sorting

## The `cn` Utility Function

We've created a `cn` utility function that combines `clsx` and `tailwind-merge`:

```ts
import { cn } from "~/utils/cn"
```

## Usage Examples

### 1. Basic Class Combination

```vue
<template>
  <div :class="cn('px-4 py-2', 'bg-blue-500 text-white')">
    Basic combination
  </div>
</template>
```

### 2. Conditional Classes

```vue
<script setup>
const isActive = ref(true)
const isLarge = ref(false)

const buttonClasses = computed(() => cn(
  'px-4 py-2 rounded',
  {
    'bg-blue-500 text-white': isActive.value,
    'bg-gray-300 text-gray-700': !isActive.value,
    'text-lg px-6 py-3': isLarge.value,
  }
))
</script>

<template>
  <button :class="buttonClasses">
    Conditional Button
  </button>
</template>
```

### 3. Conflict Resolution

```vue
<script setup>
// tailwind-merge automatically resolves conflicts
const classes = cn(
  'px-2 px-4',      // px-4 wins (last one)
  'bg-red-500',     
  'bg-blue-500',    // bg-blue-500 wins (last one)
  'py-1 py-3'       // py-3 wins (last one)
)
// Result: "px-4 bg-blue-500 py-3"
</script>
```

### 4. Component Props Integration

```vue
<script setup lang="ts">
interface Props {
  variant?: 'primary' | 'secondary'
  size?: 'sm' | 'lg'
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'sm'
})

const buttonClasses = computed(() => cn(
  // Base styles
  'inline-flex items-center justify-center rounded font-medium',
  
  // Size variants
  {
    'px-3 py-1 text-sm': props.size === 'sm',
    'px-6 py-3 text-lg': props.size === 'lg',
  },
  
  // Color variants
  {
    'bg-blue-600 text-white hover:bg-blue-700': props.variant === 'primary',
    'bg-gray-200 text-gray-900 hover:bg-gray-300': props.variant === 'secondary',
  },
  
  // Custom classes from props (will override defaults if conflicts)
  props.className
))
</script>

<template>
  <button :class="buttonClasses">
    <slot />
  </button>
</template>
```

## Best Practices

### 1. Use `cn` for All Dynamic Classes

❌ **Don't do this:**
```vue
<div :class="`px-4 py-2 ${isActive ? 'bg-blue-500' : 'bg-gray-300'}`">
```

✅ **Do this:**
```vue
<div :class="cn('px-4 py-2', { 'bg-blue-500': isActive, 'bg-gray-300': !isActive })">
```

### 2. Organize Classes Logically

```vue
<script setup>
const classes = cn(
  // Base/layout styles first
  'flex items-center justify-center',
  
  // Spacing
  'px-4 py-2 gap-2',
  
  // Typography
  'text-sm font-medium',
  
  // Colors/backgrounds
  'bg-blue-500 text-white',
  
  // Interactive states
  'hover:bg-blue-600 focus:outline-none',
  
  // Conditional styles
  {
    'opacity-50 cursor-not-allowed': disabled,
    'animate-pulse': loading,
  },
  
  // Custom overrides last
  className
)
</script>
```

### 3. Component Variants Pattern

```vue
<script setup lang="ts">
const variants = {
  primary: 'bg-blue-600 text-white hover:bg-blue-700',
  secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',
  danger: 'bg-red-600 text-white hover:bg-red-700',
}

const sizes = {
  sm: 'px-3 py-1 text-sm',
  md: 'px-4 py-2',
  lg: 'px-6 py-3 text-lg',
}

const buttonClasses = computed(() => cn(
  'inline-flex items-center justify-center rounded font-medium transition-colors',
  variants[props.variant],
  sizes[props.size],
  props.className
))
</script>
```

## Prettier Integration

The `prettier-plugin-tailwindcss` automatically sorts classes in:

- Regular `class` attributes
- `:class` bindings
- `cn()` function calls
- `clsx()` function calls

Classes are sorted according to Tailwind's recommended order:
1. Layout (display, position, etc.)
2. Flexbox & Grid
3. Spacing (margin, padding)
4. Sizing
5. Typography
6. Backgrounds & Borders
7. Effects & Filters
8. Transitions & Animations
9. Transforms
10. Interactivity

## Performance Notes

- `clsx` is extremely lightweight (~200 bytes)
- `tailwind-merge` is optimized for runtime performance
- The `cn` utility adds minimal overhead
- Prettier sorting happens at build time, not runtime

## Common Patterns

### Loading States
```vue
<button :class="cn(baseClasses, { 'opacity-50 cursor-wait': loading })">
  {{ loading ? 'Loading...' : 'Submit' }}
</button>
```

### Responsive Variants
```vue
<div :class="cn('grid gap-4', 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3')">
```

### Dark Mode Support
```vue
<div :class="cn('bg-white text-black', 'dark:bg-gray-900 dark:text-white')">
```
